package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/admin/console"
	"github.com/envoyproxy/gateway/internal/envoygateway/config"
	"github.com/envoyproxy/gateway/internal/logging"
	"github.com/envoyproxy/gateway/internal/message"
)

func main() {
	// Create a test configuration
	cfg := &config.Server{
		EnvoyGateway: &egv1a1.EnvoyGateway{
			TypeMeta: egv1a1.TypeMeta{
				Kind:       egv1a1.KindEnvoyGateway,
				APIVersion: egv1a1.GroupVersion.String(),
			},
			EnvoyGatewaySpec: egv1a1.EnvoyGatewaySpec{
				Gateway: &egv1a1.Gateway{
					ControllerName: "gateway.envoyproxy.io/gatewayclass-controller",
				},
				Provider: &egv1a1.EnvoyGatewayProvider{
					Type: egv1a1.ProviderTypeKuberne<PERSON>,
					Kubernetes: &egv1a1.EnvoyGatewayKubernetesProvider{
						Watch: &egv1a1.KubernetesWatchMode{
							Type: egv1a1.KubernetesWatchModeTypeNamespaces,
							Namespaces: []string{"default", "test-namespace"},
						},
					},
				},
				Logging: &egv1a1.EnvoyGatewayLogging{
					Level: map[egv1a1.EnvoyGatewayLogComponent]egv1a1.LogLevel{
						egv1a1.LogComponentGatewayDefault: egv1a1.LogLevelInfo,
						egv1a1.LogComponentProvider:      egv1a1.LogLevelDebug,
					},
				},
				Admin: &egv1a1.EnvoyGatewayAdmin{
					Address: &egv1a1.EnvoyGatewayAdminAddress{
						Host: "127.0.0.1",
						Port: 19000,
					},
					EnablePprof: true,
				},
			},
		},
		Logger: logging.DefaultLogger(os.Stdout, egv1a1.LogLevelInfo),
	}

	// Create handler
	handler := console.NewHandler(cfg, (*message.ProviderResources)(nil))

	// Create HTTP server
	mux := http.NewServeMux()
	handler.RegisterRoutes(mux)

	// Start server
	server := &http.Server{
		Addr:    ":8080",
		Handler: mux,
	}

	go func() {
		log.Printf("Starting admin console on http://localhost:8080")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed: %v", err)
		}
	}()

	// Wait a moment for server to start
	time.Sleep(time.Second)

	// Test the new API endpoint
	resp, err := http.Get("http://localhost:8080/api/envoy_gateway_config")
	if err != nil {
		log.Fatalf("Failed to call API: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Fatalf("API returned status %d", resp.StatusCode)
	}

	var envoyGateway egv1a1.EnvoyGateway
	if err := json.NewDecoder(resp.Body).Decode(&envoyGateway); err != nil {
		log.Fatalf("Failed to decode response: %v", err)
	}

	// Pretty print the configuration
	configJSON, err := json.MarshalIndent(envoyGateway, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal config: %v", err)
	}

	fmt.Println("✅ EnvoyGateway Configuration API Test Successful!")
	fmt.Println("📋 Configuration:")
	fmt.Println(string(configJSON))

	// Test the main page
	resp2, err := http.Get("http://localhost:8080/")
	if err != nil {
		log.Fatalf("Failed to call main page: %v", err)
	}
	defer resp2.Body.Close()

	if resp2.StatusCode != http.StatusOK {
		log.Fatalf("Main page returned status %d", resp2.StatusCode)
	}

	fmt.Println("✅ Main page accessible!")
	fmt.Println("🌐 You can view the admin console at: http://localhost:8080")
	fmt.Println("🔧 EnvoyGateway config API at: http://localhost:8080/api/envoy_gateway_config")

	// Keep server running for manual testing
	fmt.Println("Press Ctrl+C to stop the server...")
	select {}
}
