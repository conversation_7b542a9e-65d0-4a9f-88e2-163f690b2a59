apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: test-envoy-gateway
  namespace: envoy-gateway-system
spec:
  gateway:
    controllerName: gateway.envoyproxy.io/gatewayclass-controller
  provider:
    type: Kubernetes
    kubernetes:
      watch:
        type: Namespaces
        namespaces:
        - default
        - test-namespace
  logging:
    level:
      default: info
      provider: debug
  admin:
    address:
      host: "127.0.0.1"
      port: 19000
    enablePprof: true
