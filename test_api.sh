#!/bin/bash

echo "🧪 Testing EnvoyGateway Admin Console API"
echo "========================================"

# Test if we can build the project
echo "📦 Building envoy-gateway..."
cd /Users/<USER>/github/gateway
make build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

# Create a simple test config
cat > test-simple-config.yaml << EOF
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: test-config
spec:
  gateway:
    controllerName: gateway.envoyproxy.io/gatewayclass-controller
  provider:
    type: Kubernetes
  logging:
    level:
      default: info
  admin:
    address:
      host: "127.0.0.1"
      port: 19000
    enablePprof: true
EOF

echo "📋 Created test configuration"

# Test the API endpoint directly using unit tests
echo "🧪 Running API tests..."
go test ./internal/admin/console/... -run "TestHandleAPIEnvoyGatewayConfig" -v

if [ $? -eq 0 ]; then
    echo "✅ API tests passed!"
    echo "🎉 EnvoyGateway configuration API is working correctly"
    echo ""
    echo "📝 Summary of changes:"
    echo "  - Added new API endpoint: /api/envoy_gateway_config"
    echo "  - Added EnvoyGateway configuration display to main page"
    echo "  - Added JavaScript functions to load and display config"
    echo "  - Added comprehensive tests for the new functionality"
    echo ""
    echo "🌐 When running envoy-gateway, you can access:"
    echo "  - Main admin console: http://localhost:19000/"
    echo "  - EnvoyGateway config API: http://localhost:19000/api/envoy_gateway_config"
else
    echo "❌ API tests failed"
    exit 1
fi
