<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Envoy Gateway AI Assistant</title>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      #kapa-widget-container {
        height: 100vh;
        width: 100vw;
      }
    </style>
    <script
      src="https://widget.kapa.ai/kapa-widget.bundle.js"
      data-website-id="b232d295-36d9-4b0f-95da-a4524b622ef0"
      data-project-name="Envoy Gateway"
      data-project-color="#9333EA"
      data-project-logo="/img/ask-ai-green.png"
      data-modal-disclaimer="This AI assistant can help you find information about **Envoy Gateway** and **Proxy**. Please verify important information from our official documentation. If you need more help you can always [ask a human](https://github.com/envoyproxy/gateway/discussions/new?category=q-a)."
      data-modal-disclaimer-text="This AI assistant can help you find information about Envoy Gateway. Please verify important information from our official documentation."
      data-modal-example-questions="How do I install Envoy Gateway?,How do I authorize requests with EG?,What EG deployment options are there?,Can EG route outside of Kubernetes?,How do I configure TLS with EG?, Explain Envoy Gateway vs Proxy."
      data-modal-ask-ai-button-text="Ask AI"
      data-modal-title="Envoy Gateway Assistant"
      data-modal-full-screen="true"
      data-modal-open-by-default="true"
      data-modal-close-button-hide="true"
      data-modal-with-overlay="false"
      data-search-mode-enabled="true"
      data-search-include-source-names='["Envoy Gateway"]'
      data-answer-cta-button-enabled="true"
      data-answer-cta-button-link="https://github.com/envoyproxy/gateway/discussions/new?category=q-a"
      data-answer-cta-button-text="Need more help? Ask a human!"
      data-modal-border-radius="8px"
      data-modal-header-bg-color="#F5F3FF"
      data-modal-body-bg-color="#ffffff"
      data-modal-title-color="#1E1B4B"
      data-modal-title-font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
      data-modal-title-font-weight="600"
      data-modal-disclaimer-bg-color="#F5F3FF"
      data-modal-disclaimer-text-color="#4F46E5"
      data-query-input-border-color="#E9E8FF"
      data-query-input-focus-border-color="#9333EA"
      data-submit-query-button-bg-color="#9333EA"
      data-answer-cta-button-bg-color="#4F46E5"
      data-answer-cta-button-text-color="#ffffff"
      data-answer-cta-button-hover-bg-color="#6366F1"
      data-answer-cta-button-border-radius="6px"
      data-example-question-button-bg-color="#F5F3FF"
      data-example-question-button-text-color="#4F46E5"
      data-example-question-button-hover-bg-color="#E9E8FF"
      data-example-question-button-border="1px solid #E9E8FF"
      data-example-question-button-border-radius="6px"
      data-hyperlink-color="#9333EA"
      data-answer-feedback-button-active-bg-color="#F5F3FF"
      data-answer-feedback-button-active-text-color="#9333EA"
      data-answer-feedback-button-hover-bg-color="#F5F3FF"
      data-answer-feedback-button-border-radius="4px"
    ></script>
  </head>
  <body>
    <div id="kapa-widget-container"></div>
  </body>
</html>
