{{- $pagePrefix := (index (split $.Page.File.Dir "/") 0) -}}
{{- with (eq $pagePrefix "latest") -}}
{{- "v0.0.0-latest" -}}
{{- end -}}
{{- with (strings.HasPrefix $pagePrefix "v1.1") -}}
{{- "v1.1.4" -}}
{{- end -}}
{{- with (strings.HasPrefix $pagePrefix "v1.2") -}}
{{- "v1.2.6" -}}
{{- end -}}
{{- with (strings.HasPrefix $pagePrefix "v1.3") -}}
{{- "v1.3.3" -}}
{{- end -}}
{{- with (strings.HasPrefix $pagePrefix "v1.4") -}}
{{- "v1.4.2" -}}
{{- end -}}
{{- with (strings.HasPrefix $pagePrefix "docs") -}}
{{- "v1.4.2" -}}
{{- end -}}
