---
title: "v1.4.2"
publishdate: 2025-07-04
---

Date: July 4, 2025

## Security Updates

- Disabled `automountServiceAccountToken` for proxy and ratelimit deployments and serviceAccounts.

## Bug Fixes

- Fixed issue where `EnvoyExtensionPolicy` ExtProc body processing mode was set to FullDuplexStreamed, but trailers were not sent.
- Fixed validation issue where `EnvoyExtensionPolicy` ExtProc with failOpen set to true did not reject the FullDuplexStreamed body processing mode.
- Fixed issue where `EnvoyPatchPolicy` could not replace the telemetry cluster.
- Added validation for section names in Gateway listeners.
- Added ConfigMap indexers for `EnvoyExtensionPolicies` to reconcile Lua changes.
- Fixed issue where the default access log format was not working.
- Fixed bug where backendRequestTimeout was incorrectly set when retries were enabled.
- Fixed certificate SANs overlap detection in listeners.
- Fixed issue where telemetry did not work when using host port.
- Fixed bug where `BackendTLSPolicy` incorrectly referenced ConfigMaps or Secrets across namespaces.
