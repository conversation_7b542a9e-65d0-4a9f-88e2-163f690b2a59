apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-to-backend-fqdn
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /backend-fqdn
    backendRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-fqdn
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-fqdn
  namespace: gateway-conformance-infra
spec:
  endpoints:
  - fqdn:
      hostname: infra-backend-v1.gateway-conformance-infra.svc.cluster.local
      port: 8080
