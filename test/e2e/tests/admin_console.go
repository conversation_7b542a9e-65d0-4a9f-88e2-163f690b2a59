// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

//go:build e2e

package tests

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/portforward"
	"k8s.io/client-go/transport/spdy"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/gateway-api/conformance/utils/suite"
	"sigs.k8s.io/gateway-api/conformance/utils/tlog"
)

func init() {
	ConformanceTests = append(ConformanceTests, AdminConsoleTest)
}

var AdminConsoleTest = suite.ConformanceTest{
	ShortName:   "AdminConsole",
	Description: "Test Envoy Gateway Admin Console functionality",
	Manifests:   []string{},
	Test: func(t *testing.T, suite *suite.ConformanceTestSuite) {
		t.Run("AdminConsole", func(t *testing.T) {
			// Get the Envoy Gateway admin service
			nn := types.NamespacedName{Name: "envoy-gateway", Namespace: "envoy-gateway-system"}

			// Wait for the service to be ready
			if err := wait.PollUntilContextTimeout(context.TODO(), time.Second, time.Minute, true,
				func(_ context.Context) (done bool, err error) {
					svc := corev1.Service{}
					if err := suite.Client.Get(context.Background(), nn, &svc); err != nil {
						return false, nil
					}
					return true, nil
				}); err != nil {
				t.Errorf("failed to get service %s : %v", nn.String(), err)
				return
			}

			// Test admin console endpoints
			testEndpoints := []struct {
				path         string
				expectedCode int
				contentType  string
				description  string
			}{
				{"/", http.StatusOK, "text/html", "Admin Console main page"},
				{"/pprof", http.StatusOK, "text/html", "pprof page"},
				{"/server_info", http.StatusOK, "text/html", "Server info page"},
				{"/config_dump", http.StatusOK, "text/html", "Config dump page"},
				{"/stats", http.StatusOK, "text/html", "Stats page"},
				{"/api/info", http.StatusOK, "application/json", "System info API"},
				{"/api/server_info", http.StatusOK, "application/json", "Server info API"},
				{"/api/config_dump", http.StatusOK, "application/json", "Config dump API"},
				{"/static/css/admin.css", http.StatusOK, "text/css", "CSS static file"},
				{"/static/js/admin.js", http.StatusOK, "application/javascript", "JavaScript static file"},
			}

			for _, tc := range testEndpoints {
				t.Run(tc.description, func(t *testing.T) {
					// Use port-forward to access the admin server
					if err := wait.PollUntilContextTimeout(context.TODO(), time.Second, 2*time.Minute, true,
						func(_ context.Context) (done bool, err error) {
							if err := testAdminEndpoint(t, suite, nn, 19000, tc.path, tc.expectedCode, tc.contentType); err != nil {
								tlog.Logf(t, "failed to test endpoint %s: %v", tc.path, err)
								return false, nil
							}
							return true, nil
						}); err != nil {
						t.Errorf("failed to test endpoint %s: %v", tc.path, err)
					}
				})
			}
		})
	},
}

// testAdminEndpoint tests a specific admin endpoint
func testAdminEndpoint(t *testing.T, suite *suite.ConformanceTestSuite, nn types.NamespacedName, port int, path string, expectedCode int, expectedContentType string) error {
	// Create a port-forward to the admin server
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Get the pod
	pods := &corev1.PodList{}
	if err := suite.Client.List(ctx, pods, &client.ListOptions{
		Namespace: nn.Namespace,
		LabelSelector: labels.SelectorFromSet(map[string]string{
			"app": "envoy-gateway",
		}),
	}); err != nil {
		return fmt.Errorf("failed to list pods: %w", err)
	}

	if len(pods.Items) == 0 {
		return fmt.Errorf("no envoy-gateway pods found")
	}

	podName := pods.Items[0].Name

	// Create port-forward
	localPort, err := GetFreePort()
	if err != nil {
		return fmt.Errorf("failed to get free port: %w", err)
	}

	stopCh := make(chan struct{})
	defer close(stopCh)

	readyCh := make(chan struct{})
	go func() {
		if err := PortForward(suite.RestConfig, nn.Namespace, podName, localPort, port, stopCh, readyCh); err != nil {
			t.Logf("port-forward failed: %v", err)
		}
	}()

	// Wait for port-forward to be ready
	select {
	case <-readyCh:
		// Port-forward is ready
	case <-time.After(10 * time.Second):
		return fmt.Errorf("port-forward not ready in time")
	}

	// Make HTTP request
	url := fmt.Sprintf("http://localhost:%d%s", localPort, path)
	resp, err := http.Get(url) //nolint:gosec
	if err != nil {
		return fmt.Errorf("failed to make request to %s: %w", url, err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != expectedCode {
		return fmt.Errorf("expected status code %d, got %d for %s", expectedCode, resp.StatusCode, path)
	}

	// Check content type
	contentType := resp.Header.Get("Content-Type")
	if !strings.Contains(contentType, expectedContentType) {
		return fmt.Errorf("expected content type to contain %s, got %s for %s", expectedContentType, contentType, path)
	}

	// Read response body for additional validation
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Validate response content based on endpoint
	switch path {
	case "/":
		if !strings.Contains(string(body), "Envoy Gateway Admin Console") {
			return fmt.Errorf("main page does not contain expected title")
		}
	case "/api/info":
		if !strings.Contains(string(body), "version") || !strings.Contains(string(body), "uptime") {
			return fmt.Errorf("system info API does not contain expected fields")
		}
	case "/api/server_info":
		if !strings.Contains(string(body), "state") || !strings.Contains(string(body), "components") {
			return fmt.Errorf("server info API does not contain expected fields")
		}
	case "/api/config_dump":
		if !strings.Contains(string(body), "gateways") || !strings.Contains(string(body), "lastUpdated") {
			return fmt.Errorf("config dump API does not contain expected fields")
		}
	case "/static/css/admin.css":
		if !strings.Contains(string(body), "Envoy Gateway Admin Console Styles") {
			return fmt.Errorf("CSS file does not contain expected header comment")
		}
	case "/static/js/admin.js":
		if !strings.Contains(string(body), "EnvoyGatewayAdmin") {
			return fmt.Errorf("JavaScript file does not contain expected object")
		}
	}

	tlog.Logf(t, "Successfully tested endpoint %s", path)
	return nil
}

// GetFreePort returns a free port number
func GetFreePort() (int, error) {
	addr, err := net.Listen("tcp", "localhost:0")
	if err != nil {
		return 0, err
	}
	defer addr.Close()

	return addr.Addr().(*net.TCPAddr).Port, nil
}

// PortForward creates a port-forward to a pod
func PortForward(config *rest.Config, namespace, pod string, localPort, remotePort int, stopCh <-chan struct{}, readyCh chan<- struct{}) error {
	roundTripper, upgrader, err := spdy.RoundTripperFor(config)
	if err != nil {
		return err
	}

	path := fmt.Sprintf("/api/v1/namespaces/%s/pods/%s/portforward", namespace, pod)
	hostIP := strings.TrimPrefix(config.Host, "https://")
	serverURL := url.URL{Scheme: "https", Path: path, Host: hostIP}

	dialer := spdy.NewDialer(upgrader, &http.Client{Transport: roundTripper}, http.MethodPost, &serverURL)

	ports := []string{fmt.Sprintf("%d:%d", localPort, remotePort)}

	forwarder, err := portforward.New(dialer, ports, stopCh, make(chan struct{}), io.Discard, io.Discard)
	if err != nil {
		return err
	}

	return forwarder.ForwardPorts()
}
