// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package admin

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/envoygateway/config"
	"github.com/envoyproxy/gateway/internal/logging"
	"github.com/envoyproxy/gateway/internal/message"
)

func TestInitAdminServer(t *testing.T) {
	svrConfig := &config.Server{
		EnvoyGateway: &egv1a1.EnvoyGateway{
			EnvoyGatewaySpec: egv1a1.EnvoyGatewaySpec{},
		},
	}

	svrConfig.Logger = logging.NewLogger(os.Stdout, egv1a1.DefaultEnvoyGatewayLogging())
	runner := New(&Config{
		Server: *svrConfig,
	})
	err := runner.Start(context.Background())
	require.NoError(t, err)

	// Clean up
	err = runner.Close()
	require.NoError(t, err)
}

func TestAdminServerWithConsole(t *testing.T) {
	// Create a test server (console is always enabled)
	cfg := &config.Server{
		EnvoyGateway: &egv1a1.EnvoyGateway{
			EnvoyGatewaySpec: egv1a1.EnvoyGatewaySpec{
				Admin: &egv1a1.EnvoyGatewayAdmin{
					Address: &egv1a1.EnvoyGatewayAdminAddress{
						Host: "127.0.0.1",
						Port: 0, // Use a random port
					},
				},
			},
		},
		Logger: logging.DefaultLogger(io.Discard, egv1a1.LogLevelInfo),
	}

	providerResources := &message.ProviderResources{}
	runner := New(&Config{
		Server:            *cfg,
		ProviderResources: providerResources,
	})

	// Start the server
	err := runner.Start(context.Background())
	require.NoError(t, err)
	defer runner.Close()

	// Get the address
	addressStr := cfg.EnvoyGateway.GetEnvoyGatewayAdminAddress()
	url := fmt.Sprintf("http://%s", addressStr)

	// Wait for the server to start
	waitForServer(t, url)

	// Test console endpoints
	testEndpoints := []struct {
		path         string
		expectedCode int
		contentType  string
	}{
		{"/", http.StatusOK, "text/html"},
		{"/api/info", http.StatusOK, "application/json"},
	}

	for _, tc := range testEndpoints {
		t.Run(tc.path, func(t *testing.T) {
			resp, err := http.Get(url + tc.path)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tc.expectedCode, resp.StatusCode)
			assert.Contains(t, resp.Header.Get("Content-Type"), tc.contentType)
		})
	}
}

// waitForServer waits for the server to start
func waitForServer(t *testing.T, url string) {
	// Wait for the server to start
	maxRetries := 10
	for i := 0; i < maxRetries; i++ {
		resp, err := http.Get(url) //nolint:gosec
		if err == nil {
			resp.Body.Close()
			return
		}
		time.Sleep(100 * time.Millisecond)
	}
	t.Fatalf("Server did not start in time")
}
