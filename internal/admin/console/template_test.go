// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package console

import (
	"bytes"
	"html/template"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTemplateLoading(t *testing.T) {
	// Test template loading
	templates, err := template.ParseFS(templateFiles, "templates/*.html")
	require.NoError(t, err)

	// Test each template individually
	testCases := []struct {
		templateName string
		expectedText string
	}{
		{"index.html", "Welcome to the Envoy Gateway Admin Console"},
		{"server_info.html", "displays detailed information about the Envoy Gateway server"},
		{"config_dump.html", "displays the current configuration state"},
		{"stats.html", "provides access to Envoy Gateway statistics"},
	}

	for _, tc := range testCases {
		t.Run(tc.templateName, func(t *testing.T) {
			var buf bytes.Buffer
			data := struct {
				Title          string
				MetricsAddress string
				EnablePprof    bool
			}{
				Title:          "Test",
				MetricsAddress: "localhost:19001",
				EnablePprof:    true,
			}

			err := templates.ExecuteTemplate(&buf, tc.templateName, data)
			require.NoError(t, err)

			content := buf.String()
			assert.Contains(t, content, tc.expectedText, "Template %s should contain expected text", tc.templateName)

			// Debug: print first 200 characters of each template
			t.Logf("Template %s content (first 200 chars): %s", tc.templateName, content[:min(200, len(content))])
		})
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
