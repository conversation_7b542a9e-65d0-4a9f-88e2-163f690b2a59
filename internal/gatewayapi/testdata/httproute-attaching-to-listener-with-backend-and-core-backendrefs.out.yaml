backends:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-uds
    namespace: default
  spec:
    endpoints:
    - unix:
        path: /var/run/backend.sock
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-fqdn
    namespace: default
  spec:
    endpoints:
    - fqdn:
        hostname: primary.foo.com
        port: 3000
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-ip
    namespace: default
  spec:
    endpoints:
    - ip:
        address: *******
        port: 3001
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 2
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-fqdn
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-fqdn
      - name: service-fqdn
        port: 8080
      - group: multicluster.x-k8s.io
        kind: ServiceImport
        name: service-import-fqdn
        port: 8081
      matches:
      - path:
          value: /2
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-static
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-ip
      - name: service-ip
        port: 8080
      - group: multicluster.x-k8s.io
        kind: ServiceImport
        name: service-import-ip
        port: 8081
      matches:
      - path:
          value: /1
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-fqdn
            namespace: default
          name: httproute/default/httproute-fqdn/rule/0
          settings:
          - addressType: FQDN
            endpoints:
            - host: primary.foo.com
              port: 3000
            metadata:
              kind: Backend
              name: backend-fqdn
              namespace: default
            name: httproute/default/httproute-fqdn/rule/0/backend/0
            protocol: HTTP
            weight: 1
          - addressType: FQDN
            endpoints:
            - host: bar.foo
              port: 8080
            metadata:
              kind: Service
              name: service-fqdn
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-fqdn/rule/0/backend/1
            protocol: HTTP
            weight: 1
          - addressType: FQDN
            endpoints:
            - host: foo.bar
              port: 8080
            metadata:
              kind: ServiceImport
              name: service-import-fqdn
              namespace: default
              sectionName: "8081"
            name: httproute/default/httproute-fqdn/rule/0/backend/2
            protocol: HTTP
            weight: 1
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-fqdn
          namespace: default
        name: httproute/default/httproute-fqdn/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /2
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-static
            namespace: default
          name: httproute/default/httproute-static/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 3001
            metadata:
              kind: Backend
              name: backend-ip
              namespace: default
            name: httproute/default/httproute-static/rule/0/backend/0
            protocol: HTTP
            weight: 1
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              kind: Service
              name: service-ip
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-static/rule/0/backend/1
            protocol: HTTP
            weight: 1
          - addressType: IP
            endpoints:
            - host: *******
              port: 8081
            metadata:
              kind: ServiceImport
              name: service-import-ip
              namespace: default
              sectionName: "8081"
            name: httproute/default/httproute-static/rule/0/backend/2
            protocol: HTTP
            weight: 1
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-static
          namespace: default
        name: httproute/default/httproute-static/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /1
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
