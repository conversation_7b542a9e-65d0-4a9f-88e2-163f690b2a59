gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      hostname: '*.envoyproxy.io'
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 6
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-header-and-backend-host-rewrites
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      filters:
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-header
        type: ExtensionRef
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-header
        type: ExtensionRef
      matches:
      - path:
          value: /header-and-backend
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Cannot configure multiple urlRewrite filters for a single HTTPRouteRule
        reason: UnsupportedValue
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-invalid-header
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      filters:
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: invalid-header
        type: ExtensionRef
      matches:
      - path:
          value: /invalid-header
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Header must be set when rewrite path type is "Header"
        reason: UnsupportedValue
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-multiple-header-host-rewrites
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      filters:
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-header
        type: ExtensionRef
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-header-2
        type: ExtensionRef
      matches:
      - path:
          value: /two-headers
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Cannot configure multiple urlRewrite filters for a single HTTPRouteRule
        reason: UnsupportedValue
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-multiple-header-host-rewrites
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      filters:
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-backend
        type: ExtensionRef
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-backend-2
        type: ExtensionRef
      matches:
      - path:
          value: /two-backends
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Cannot configure multiple urlRewrite filters for a single HTTPRouteRule
        reason: UnsupportedValue
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-multiple-host-rewrites-1
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      filters:
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-header
        type: ExtensionRef
      - type: URLRewrite
        urlRewrite:
          hostname: rewrite.com
      matches:
      - path:
          value: /ext-first
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Cannot configure multiple urlRewrite filters for a single HTTPRouteRule
        reason: UnsupportedValue
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-multiple-path-rewrites-2
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      filters:
      - type: URLRewrite
        urlRewrite:
          hostname: rewrite.com
      - extensionRef:
          group: gateway.envoyproxy.io
          kind: HTTPRouteFilter
          name: valid-header
        type: ExtensionRef
      matches:
      - path:
          value: /inline-first
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Cannot configure multiple urlRewrite filters for a single HTTPRouteRule
        reason: UnsupportedValue
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*.envoyproxy.io'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
