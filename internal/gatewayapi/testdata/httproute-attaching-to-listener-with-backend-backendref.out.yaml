backends:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-uds
    namespace: default
  spec:
    endpoints:
    - unix:
        path: /var/run/backend.sock
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-fqdn
    namespace: default
  spec:
    endpoints:
    - fqdn:
        hostname: primary.foo.com
        port: 3000
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-ip
    namespace: default
  spec:
    endpoints:
    - ip:
        address: *******
        port: 3001
  status:
    conditions:
    - lastTransitionTime: null
      message: The Backend was accepted
      reason: Accepted
      status: "True"
      type: Accepted
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-ip-localhost
    namespace: default
  spec:
    endpoints:
    - ip:
        address: 127.0.0.1
        port: 3001
  status:
    conditions:
    - lastTransitionTime: null
      message: 'The Backend was not accepted: IP address 127.0.0.1 in the loopback
        range is not supported'
      reason: Accepted
      status: "False"
      type: Invalid
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: Backend
  metadata:
    creationTimestamp: null
    name: backend-fqdn-localhost
    namespace: default
  spec:
    endpoints:
    - fqdn:
        hostname: localhost
        port: 3001
  status:
    conditions:
    - lastTransitionTime: null
      message: 'The Backend was not accepted: hostname localhost should be a domain
        with at least two segments separated by dots'
      reason: Accepted
      status: "False"
      type: Invalid
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 5
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-uds
      matches:
      - path:
          value: /1
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'Failed to process route rule 0 backendRef 0: unix domain sockets
          are not supported in backend references.'
        reason: UnsupportedAddressType
        status: "False"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-ip
      matches:
      - path:
          value: /2
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-3
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-fqdn
      matches:
      - path:
          value: /3
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-4
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-ip-localhost
      matches:
      - path:
          value: /4
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'Failed to process route rule 0 backendRef 0: IP address 127.0.0.1
          in the loopback range is not supported.'
        reason: InvalidAddress
        status: "False"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-5
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - group: gateway.envoyproxy.io
        kind: Backend
        name: backend-fqdn-localhost
      matches:
      - path:
          value: /5
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'Failed to process route rule 0 backendRef 0: hostname localhost
          should be a domain with at least two segments separated by dots.'
        reason: InvalidAddress
        status: "False"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - directResponse:
          statusCode: 500
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /1
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: default
          name: httproute/default/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 3001
            metadata:
              kind: Backend
              name: backend-ip
              namespace: default
            name: httproute/default/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: default
        name: httproute/default/httproute-2/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /2
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-3
            namespace: default
          name: httproute/default/httproute-3/rule/0
          settings:
          - addressType: FQDN
            endpoints:
            - host: primary.foo.com
              port: 3000
            metadata:
              kind: Backend
              name: backend-fqdn
              namespace: default
            name: httproute/default/httproute-3/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-3
          namespace: default
        name: httproute/default/httproute-3/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /3
      - directResponse:
          statusCode: 500
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-4
          namespace: default
        name: httproute/default/httproute-4/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /4
      - directResponse:
          statusCode: 500
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-5
          namespace: default
        name: httproute/default/httproute-5/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /5
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
