gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-2
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
grpcRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: GRPCRoute
  metadata:
    namespace: default
    name: grpcroute-1
  spec:
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-2
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-2
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-2
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/route2"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-3
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-2
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/route3"
      backendRefs:
      - name: service-1
        port: 8080
backendTrafficPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    namespace: envoy-gateway
    name: policy-for-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    retry:
      retryOn:
        triggers:
        - cancelled
        - reset-before-request
      perRetry:
        timeout: 250ms
        backoff:
          baseInterval: 100ms
          maxInterval: 10s
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    namespace: default
    name: policy-for-route-1
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    retry:
      numRetries: 5
      numAttemptsPerPriority: 4
      retryOn:
        httpStatusCodes:
        - 429
        - 503
        triggers:
        - connect-failure
        - retriable-status-codes
      perRetry:
        timeout: 250ms
        backoff:
          baseInterval: 100ms
          maxInterval: 10s
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    namespace: default
    name: policy-for-route-2
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-2
    retry:
      perRetry:
        backoff:
          baseInterval: 0s
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    namespace: default
    name: policy-for-route-3
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-3
    retry:
      numAttemptsPerPriority: 0
      perRetry:
        backoff:
          baseInterval: 2s
          maxInterval: 1s
