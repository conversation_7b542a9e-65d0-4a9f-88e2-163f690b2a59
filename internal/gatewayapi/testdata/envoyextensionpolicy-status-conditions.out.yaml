envoyExtensionPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: target-httproute-in-gateway-1
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: also-target-httproute-in-gateway-1
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Unable to target HTTPRoute httproute-1, another EnvoyExtensionPolicy
          has already attached to it
        reason: Conflicted
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: target-grpcroute-in-gateway-2
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: GRPCRoute
      name: grpcroute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: target-unknown-httproute
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: unknown-httproute
  status:
    ancestors: null
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: not-same-namespace-httproute
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: not-same-namespace-httproute
  status:
    ancestors: null
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-1
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'This policy is being overridden by other envoyExtensionPolicies
          for these routes: [envoy-gateway/httproute-1]'
        reason: Overridden
        status: "True"
        type: Overridden
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-1-as-well
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Unable to target Gateway gateway-1, another EnvoyExtensionPolicy
          has already attached to it
        reason: Conflicted
        status: "False"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: target-unknown-gateway
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: unknown-gateway
  status:
    ancestors: null
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: not-same-namespace-gateway
    namespace: envoy-gateway
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: not-same-namespace-gateway
  status:
    ancestors: null
gateways:
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-2
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
    - allowedRoutes:
        namespaces:
          from: Same
      name: https
      port: 443
      protocol: HTTPS
    - allowedRoutes:
        namespaces:
          from: Same
      name: tcp
      port: 53
      protocol: TCP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Listener must have TLS set when protocol is HTTPS.
        reason: Invalid
        status: "False"
        type: Programmed
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: https
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: tcp
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: TCPRoute
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: not-same-namespace-gateway
    namespace: another-namespace
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
grpcRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: GRPCRoute
  metadata:
    creationTimestamp: null
    name: grpcroute-1
    namespace: envoy-gateway
  spec:
    parentRefs:
    - name: gateway-2
      namespace: envoy-gateway
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - headers:
        - name: magic
          type: Exact
          value: foo
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-2
        namespace: envoy-gateway
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: not-same-namespace-httproute
    namespace: another-namespace
  spec:
    parentRefs:
    - name: not-same-namespace-gateway
      namespace: another-namespace
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: No listeners included by this parent ref allowed this attachment.
        reason: NotAllowedByListeners
        status: "False"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: not-same-namespace-gateway
        namespace: another-namespace
- apiVersion: gateway.networking.k8s.io/v1beta1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: envoy-gateway
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
infraIR:
  another-namespace/not-same-namespace-gateway:
    proxy:
      listeners:
      - address: null
        name: another-namespace/not-same-namespace-gateway/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: not-same-namespace-gateway
          gateway.envoyproxy.io/owning-gateway-namespace: another-namespace
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: another-namespace/not-same-namespace-gateway
      namespace: envoy-gateway-system
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
  envoy-gateway/gateway-2:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-2/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      - address: null
        name: envoy-gateway/gateway-2/tcp
        ports:
        - containerPort: 10053
          name: tcp-53
          protocol: TCP
          servicePort: 53
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-2
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-2
      namespace: envoy-gateway-system
xdsIR:
  another-namespace/not-same-namespace-gateway:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: not-same-namespace-gateway
        namespace: another-namespace
        sectionName: http
      name: another-namespace/not-same-namespace-gateway/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: envoy-gateway
          name: httproute/envoy-gateway/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              kind: Service
              name: service-1
              namespace: envoy-gateway
              sectionName: "8080"
            name: httproute/envoy-gateway/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        envoyExtensions: {}
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: envoy-gateway
        name: httproute/envoy-gateway/httproute-1/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-2:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: true
      metadata:
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-2/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: GRPCRoute
            name: grpcroute-1
            namespace: envoy-gateway
          name: grpcroute/envoy-gateway/grpcroute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              kind: Service
              name: service-1
              namespace: envoy-gateway
              sectionName: "8080"
            name: grpcroute/envoy-gateway/grpcroute-1/rule/0/backend/0
            protocol: GRPC
            weight: 1
        envoyExtensions: {}
        headerMatches:
        - distinct: false
          exact: foo
          name: magic
        hostname: '*'
        isHTTP2: true
        metadata:
          kind: GRPCRoute
          name: grpcroute-1
          namespace: envoy-gateway
        name: grpcroute/envoy-gateway/grpcroute-1/rule/0/match/0/*
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
    tcp:
    - address: 0.0.0.0
      name: envoy-gateway/gateway-2/tcp
      port: 10053
