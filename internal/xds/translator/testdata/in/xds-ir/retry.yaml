http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "first-route"
    hostname: "*"
    retry:
      numRetries: 3
      perRetry:
        backOff:
          baseInterval: 200ms
      retryOn:
        httpStatusCodes:
        - 500
    traffic:
      retry:
        numRetries: 5
        numAttemptsPerPriority: 0
        retryOn:
          httpStatusCodes:
          - 429
          - 503
          triggers:
          - reset
          - connect-failure
          - retriable-status-codes
        perRetry:
          timeout: 250ms
          backoff:
            baseInterval: 100ms
            maxInterval: 10s
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
  - name: "second-route-defaults"
    hostname: "foo"
    traffic:
      retry: {}
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
  - name: "third-route-only-triggers"
    hostname: "foo"
    traffic:
      retry:
        numRetries: 5
        numAttemptsPerPriority: 3
        retryOn:
          triggers:
          - reset
          - reset-before-request
          - connect-failure
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
