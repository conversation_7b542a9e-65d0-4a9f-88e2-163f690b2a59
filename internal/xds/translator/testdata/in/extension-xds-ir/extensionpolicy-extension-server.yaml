extensionServerPolicies:
- Object:
    apiVersion: security.example.io/v1alpha1
    kind: ExampleExtPolicy
    metadata:
      name: ext-server-policy-test
      namespace: test
    spec:
      data: attached to all clusters
      targetRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
extensionManagerConfig:
  hooks:
    xdsTranslator:
      translation:
        listener:
          includeAll: true
        route:
          includeAll: true
