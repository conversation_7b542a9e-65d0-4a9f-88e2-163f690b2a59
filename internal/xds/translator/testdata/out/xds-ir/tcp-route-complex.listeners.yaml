- address:
    socketAddress:
      address: '::'
      portValue: 10080
  filterChains:
  - filterChainMatch:
      serverNames:
      - foo.com
      - bar.com
      - example.com
    filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tcp-route-complex-dest
        statPrefix: tls-passthrough-10080
    name: tcp-route-complex
  listenerFilters:
  - name: envoy.filters.listener.tls_inspector
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
  maxConnectionsToAcceptPerSocketEvent: 1
  name: tcp-listener-complex
  perConnectionBufferLimitBytes: 32768
