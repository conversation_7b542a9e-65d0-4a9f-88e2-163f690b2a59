- ignorePortInHostMatching: true
  name: first-listener
  virtualHosts:
  - domains:
    - '*'
    name: first-listener/*
    routes:
    - match:
        prefix: /
      name: first-route
      route:
        cluster: first-route-dest
        retryPolicy:
          hostSelectionRetryMaxAttempts: "5"
          numRetries: 3
          retriableStatusCodes:
          - 500
          retryBackOff:
            baseInterval: 0.200s
          retryHostPredicate:
          - name: envoy.retry_host_predicates.previous_hosts
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.retry.host.previous_hosts.v3.PreviousHostsPredicate
          retryOn: connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes
        upgradeConfigs:
        - upgradeType: websocket
  - domains:
    - foo
    name: first-listener/foo
    routes:
    - match:
        prefix: /
      name: second-route-defaults
      route:
        cluster: first-route-dest
        retryPolicy:
          hostSelectionRetryMaxAttempts: "5"
          numRetries: 2
          retriableStatusCodes:
          - 503
          retryHostPredicate:
          - name: envoy.retry_host_predicates.previous_hosts
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.retry.host.previous_hosts.v3.PreviousHostsPredicate
          retryOn: connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: third-route-only-triggers
      route:
        cluster: first-route-dest
        retryPolicy:
          hostSelectionRetryMaxAttempts: "5"
          numRetries: 5
          retryHostPredicate:
          - name: envoy.retry_host_predicates.previous_hosts
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.retry.host.previous_hosts.v3.PreviousHostsPredicate
          retryOn: reset,reset-before-request,connect-failure
          retryPriority:
            name: envoy.retry_priorities.previous_priorities
            typedConfig:
              '@type': type.googleapis.com/envoy.extensions.retry.priority.previous_priorities.v3.PreviousPrioritiesConfig
              updateFrequency: 3
        upgradeConfigs:
        - upgradeType: websocket
