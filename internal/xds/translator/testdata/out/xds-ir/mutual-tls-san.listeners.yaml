- address:
    socketAddress:
      address: '::'
      portValue: 10080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: first-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: https-10080
        useRemoteAddress: true
    name: first-listener
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
        commonTlsContext:
          alpnProtocols:
          - h2
          - http/1.1
          combinedValidationContext:
            defaultValidationContext:
              matchTypedSubjectAltNames:
              - matcher:
                  exact: client1.example.com
                sanType: DNS
              - matcher:
                  suffix: '@example.com'
                sanType: EMAIL
              - matcher:
                  prefix: 192.168.
                sanType: IP_ADDRESS
              - matcher:
                  exact: spiffe://example.com/client1
                sanType: URI
              - matcher:
                  exact: client1
                oid: *******.4.1.311.20.2.3
                sanType: OTHER_NAME
              verifyCertificateHash:
              - df6ff72fe9116521268f6f2dd4966f51df479883fe7037b39f75916ac3049d1a
              verifyCertificateSpki:
              - NvqYIYSbgK2vCJpQhObf77vv+bQWtc5ek5RIOwPiC9A=
            validationContextSdsSecretConfig:
              name: ca-cert
              sdsConfig:
                ads: {}
                resourceApiVersion: V3
          tlsCertificateSdsSecretConfigs:
          - name: secret-1
            sdsConfig:
              ads: {}
              resourceApiVersion: V3
          - name: secret-2
            sdsConfig:
              ads: {}
              resourceApiVersion: V3
        disableStatefulSessionResumption: true
        disableStatelessSessionResumption: true
        requireClientCertificate: true
  maxConnectionsToAcceptPerSocketEvent: 1
  name: first-listener
  perConnectionBufferLimitBytes: 32768
- address:
    socketAddress:
      address: '::'
      portValue: 10081
  filterChains:
  - filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tls-terminate-dest
        statPrefix: tls-terminate-10081
    name: tls-route-terminate
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
        commonTlsContext:
          combinedValidationContext:
            defaultValidationContext:
              matchTypedSubjectAltNames:
              - matcher:
                  exact: client2.example.org
                sanType: DNS
              - matcher:
                  suffix: '@example.org'
                sanType: EMAIL
              - matcher:
                  prefix: "10."
                sanType: IP_ADDRESS
              - matcher:
                  exact: spiffe://example.com/client2
                sanType: URI
              - matcher:
                  exact: client2
                oid: *******.4.1.311.20.2.3
                sanType: OTHER_NAME
              verifyCertificateHash:
              - df6ff72fe9116521268f6f2dd4966f51df479883fe7037b39f75916ac3049d1a
              verifyCertificateSpki:
              - NvqYIYSbgK2vCJpQhObf77vv+bQWtc5ek5RIOwPiC9A=
            validationContextSdsSecretConfig:
              name: ca-cert-2
              sdsConfig:
                ads: {}
                resourceApiVersion: V3
          tlsCertificateSdsSecretConfigs:
          - name: secret-3
            sdsConfig:
              ads: {}
              resourceApiVersion: V3
        disableStatefulSessionResumption: true
        disableStatelessSessionResumption: true
        requireClientCertificate: true
  maxConnectionsToAcceptPerSocketEvent: 1
  name: second-listener
  perConnectionBufferLimitBytes: 32768
