- clusterName: first-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: first-route-dest/backend/0
- clusterName: max-accept-disabled
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: max-accept-disabled/backend/0
- clusterName: max-accept-default
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50001
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: max-accept-default/backend/0
