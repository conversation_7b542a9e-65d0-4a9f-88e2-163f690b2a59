- address:
    socketAddress:
      address: '::'
      portValue: 10080
  filterChains:
  - filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tcp-route-simple-dest
        statPrefix: tcp-10080
  maxConnectionsToAcceptPerSocketEvent: 1
  name: tcp-route-enable-endpoint-stats
  perConnectionBufferLimitBytes: 32768
