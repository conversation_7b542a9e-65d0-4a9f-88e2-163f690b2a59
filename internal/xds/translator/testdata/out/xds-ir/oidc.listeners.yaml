- address:
    socketAddress:
      address: '::'
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.oauth2/securitypolicy/default/policy-for-first-route
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.oauth2.v3.OAuth2
            config:
              authScopes:
              - openid
              - email
              - profile
              authType: BASIC_AUTH
              authorizationEndpoint: https://oauth.foo.com/oauth2/v2/auth
              credentials:
                clientId: client.oauth.foo.com
                cookieNames:
                  bearerToken: AccessToken-5F93C2E4
                  idToken: IdToken-5F93C2E4
                  oauthExpires: OauthExpires-5F93C2E4
                  oauthHmac: OauthHMAC-5F93C2E4
                  oauthNonce: OauthNonce-5F93C2E4
                  refreshToken: RefreshToken-5F93C2E4
                hmacSecret:
                  name: oauth2/hmac_secret/securitypolicy/default/policy-for-first-route
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
                tokenSecret:
                  name: oauth2/client_secret/securitypolicy/default/policy-for-first-route
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
              defaultExpiresIn: 3600s
              defaultRefreshTokenExpiresIn: 172800s
              forwardBearerToken: true
              redirectPathMatcher:
                path:
                  exact: /foo/oauth2/callback
              redirectUri: https://www.example.com/foo/oauth2/callback
              resources:
              - api
              signoutPath:
                path:
                  exact: /foo/logout
              tokenEndpoint:
                cluster: oauth_foo_com_443
                timeout: 10s
                uri: https://oauth.foo.com/token
              useRefreshToken: true
        - disabled: true
          name: envoy.filters.http.oauth2/securitypolicy/default/policy-for-second-route
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.oauth2.v3.OAuth2
            config:
              authScopes:
              - openid
              - email
              - profile
              authType: BASIC_AUTH
              authorizationEndpoint: https://oauth.bar.com/oauth2/v2/auth
              credentials:
                clientId: client.oauth.bar.com
                cookieDomain: example.com
                cookieNames:
                  bearerToken: CustomAccessTokenOverride
                  idToken: CustomIdTokenOverride
                  oauthExpires: OauthExpires-5f93c2e4
                  oauthHmac: OauthHMAC-5f93c2e4
                  oauthNonce: OauthNonce-5f93c2e4
                  refreshToken: RefreshToken-5f93c2e4
                hmacSecret:
                  name: oauth2/hmac_secret/securitypolicy/default/policy-for-second-route
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
                tokenSecret:
                  name: oauth2/client_secret/securitypolicy/default/policy-for-second-route
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
              denyRedirectMatcher:
              - name: test-exact
                stringMatch:
                  exact: /api
              - name: test-regex
                stringMatch:
                  safeRegex:
                    regex: .*
              - name: test-suffix
                stringMatch:
                  suffix: bar
              - name: test-prefix
                stringMatch:
                  prefix: foo
              - name: test-no-type
                stringMatch:
                  exact: foobar
              preserveAuthorizationHeader: true
              redirectPathMatcher:
                path:
                  exact: /bar/oauth2/callback
              redirectUri: https://www.example.com/bar/oauth2/callback
              resources:
              - api
              signoutPath:
                path:
                  exact: /bar/logout
              tokenEndpoint:
                cluster: oauth_bar_com_443
                timeout: 10s
                uri: https://oauth.bar.com/token
              useRefreshToken: false
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: first-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: first-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: first-listener
  perConnectionBufferLimitBytes: 32768
