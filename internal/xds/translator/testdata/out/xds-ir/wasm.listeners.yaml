- address:
    socketAddress:
      address: 0.0.0.0
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - disabled: true
          name: envoy.filters.http.wasm/envoyextensionpolicy/default/policy-for-http-route/wasm/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            config:
              configuration:
                '@type': type.googleapis.com/google.protobuf.StringValue
                value: '{"parameter1":{"key1":"value1"},"parameter2":{"key2":{"key3":"value3"}}}'
              failOpen: true
              name: wasm-filter-4
              vmConfig:
                code:
                  remote:
                    httpUri:
                      cluster: wasm_cluster
                      timeout: 10s
                      uri: https://envoy-gateway:18002/fe571e7b1ef5dc626ceb2c2c86782a134a92989a2643485238951696ae4334c3.wasm
                    sha256: a1f0b78b8c1320690327800e3a5de10e7dbba7b6c752e702193a395a52c727b6
                runtime: envoy.wasm.runtime.v8
                vmId: envoyextensionpolicy/default/policy-for-http-route/wasm/0
        - disabled: true
          name: envoy.filters.http.wasm/envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/0
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            config:
              configuration:
                '@type': type.googleapis.com/google.protobuf.StringValue
                value: '{"parameter1":{"key1":"value1","key2":"value2"},"parameter2":"value3"}'
              name: wasm-filter-1
              vmConfig:
                code:
                  remote:
                    httpUri:
                      cluster: wasm_cluster
                      timeout: 10s
                      uri: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/5c90b9a82642ce00a7753923fabead306b9d9a54a7c0bd2463a1af3efcfb110b.wasm
                    sha256: 746df05c8f3a0b07a46c0967cfbc5cbe5b9d48d0f79b6177eeedf8be6c8b34b5
                runtime: envoy.wasm.runtime.v8
                vmId: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/0
        - disabled: true
          name: envoy.filters.http.wasm/envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/1
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            config:
              configuration:
                '@type': type.googleapis.com/google.protobuf.StringValue
                value: '{"parameter1":"value1","parameter2":"value2"}'
              name: wasm-filter-2
              rootId: my-root-id
              vmConfig:
                code:
                  remote:
                    httpUri:
                      cluster: wasm_cluster
                      timeout: 10s
                      uri: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/7abf116e5cd5a20389604a5ba0f3bd04fdf76f92181fe67506b42c2ee596d3fd.wasm
                    sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
                runtime: envoy.wasm.runtime.v8
                vmId: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/1
        - disabled: true
          name: envoy.filters.http.wasm/envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/2
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            config:
              configuration:
                '@type': type.googleapis.com/google.protobuf.StringValue
                value: ""
              name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/2
              vmConfig:
                code:
                  remote:
                    httpUri:
                      cluster: wasm_cluster
                      timeout: 10s
                      uri: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/42d30b4a4cc631415e6e48c02d244700da327201eb273f752cacf745715b31d9.wasm
                    sha256: 2a19e4f337e5223d7287e7fccd933fb01905deaff804292e5257f8c681b82bee
                environmentVariables:
                  hostEnvKeys:
                  - SOME_KEY
                  - ANOTHER_KEY
                runtime: envoy.wasm.runtime.v8
                vmId: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/2
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: envoy-gateway/gateway-1/http
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: envoy-gateway/gateway-1/http
  maxConnectionsToAcceptPerSocketEvent: 1
  name: envoy-gateway/gateway-1/http
  perConnectionBufferLimitBytes: 32768
