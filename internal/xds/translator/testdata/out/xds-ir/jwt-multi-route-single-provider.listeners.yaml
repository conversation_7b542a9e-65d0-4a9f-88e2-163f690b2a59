- accessLog:
  - filter:
      responseFlagFilter:
        flags:
        - NR
    name: envoy.access_loggers.file
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
      logFormat:
        textFormatSource:
          inlineString: |
            this is a log
      path: /dev/stdout
  address:
    socketAddress:
      address: '::'
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        accessLog:
        - name: envoy.access_loggers.file
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
            logFormat:
              textFormatSource:
                inlineString: |
                  this is a log
            path: /dev/stdout
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.jwt_authn
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.jwt_authn.v3.JwtAuthentication
            providers:
              first-route/example:
                audiences:
                - foo.com
                claimToHeaders:
                - claimName: claim.neteased.key
                  headerName: first-route-key
                forward: true
                issuer: https://www.example.com
                normalizePayloadInMetadata:
                  spaceDelimitedClaims:
                  - scope
                payloadInMetadata: example
                remoteJwks:
                  asyncFetch: {}
                  cacheDuration: 300s
                  httpUri:
                    cluster: localhost_443
                    timeout: 10s
                    uri: https://localhost/jwt/public-key/jwks.json
              second-route/example:
                audiences:
                - foo.com
                forward: true
                issuer: https://www.example.com
                normalizePayloadInMetadata:
                  spaceDelimitedClaims:
                  - scope
                payloadInMetadata: example
                remoteJwks:
                  asyncFetch: {}
                  cacheDuration: 300s
                  httpUri:
                    cluster: localhost_443
                    timeout: 10s
                    uri: https://localhost/jwt/public-key/jwks.json
            requirementMap:
              first-route:
                providerName: first-route/example
              second-route:
                providerName: second-route/example
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: first-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: first-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: first-listener
  perConnectionBufferLimitBytes: 32768
