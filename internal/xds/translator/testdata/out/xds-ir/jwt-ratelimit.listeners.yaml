- address:
    socketAddress:
      address: '::'
      portValue: 10080
  defaultFilterChain:
    filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        commonHttpProtocolOptions:
          headersWithUnderscoresAction: REJECT_REQUEST
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
          maxConcurrentStreams: 100
        httpFilters:
        - name: envoy.filters.http.jwt_authn
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.jwt_authn.v3.JwtAuthentication
            providers:
              first-route/example:
                audiences:
                - foo.com
                forward: true
                issuer: https://www.example.com
                normalizePayloadInMetadata:
                  spaceDelimitedClaims:
                  - scope
                payloadInMetadata: example
                remoteJwks:
                  asyncFetch: {}
                  cacheDuration: 300s
                  httpUri:
                    cluster: "192_168_1_250_443"
                    timeout: 10s
                    uri: https://192.168.1.250/jwt/public-key/jwks.json
            requirementMap:
              first-route:
                providerName: first-route/example
        - name: envoy.filters.http.ratelimit
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ratelimit.v3.RateLimit
            domain: first-listener
            enableXRatelimitHeaders: DRAFT_VERSION_03
            rateLimitService:
              grpcService:
                envoyGrpc:
                  clusterName: ratelimit_cluster
              transportApiVersion: V3
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            suppressEnvoyHeaders: true
        mergeSlashes: true
        normalizePath: true
        pathWithEscapedSlashesAction: UNESCAPE_AND_REDIRECT
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: first-listener
        serverHeaderTransformation: PASS_THROUGH
        statPrefix: http-10080
        useRemoteAddress: true
    name: first-listener
  maxConnectionsToAcceptPerSocketEvent: 1
  name: first-listener
  perConnectionBufferLimitBytes: 32768
