- address:
    socketAddress:
      address: 0.0.0.0
      ipv4Compat: true
      portValue: 8082
  filterChains:
  - filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tcp-route-dual-dest
        statPrefix: tcp-8082
    name: tcp-route-dual
  maxConnectionsToAcceptPerSocketEvent: 1
  name: tcp-listener-dual
  perConnectionBufferLimitBytes: 32768
