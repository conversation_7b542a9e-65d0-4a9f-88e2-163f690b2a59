- address:
    socketAddress:
      address: '::'
      portValue: 10080
  filterChains:
  - filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tls-terminate-dest
        statPrefix: tls-terminate-10080
    name: tls-route-terminate
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
        commonTlsContext:
          tlsCertificateSdsSecretConfigs:
          - name: envoy-gateway-tls-secret-1
            sdsConfig:
              ads: {}
              resourceApiVersion: V3
        disableStatefulSessionResumption: true
        disableStatelessSessionResumption: true
  - filterChainMatch:
      serverNames:
      - '*.envoyproxy.io'
      - example.com
    filters:
    - name: envoy.filters.network.tcp_proxy
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        cluster: tls-terminate-hostname-dest
        statPrefix: tls-terminate-10080
    name: tls-terminate-hostname
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
        commonTlsContext:
          tlsCertificateSdsSecretConfigs:
          - name: envoy-gateway-tls-secret-1
            sdsConfig:
              ads: {}
              resourceApiVersion: V3
        disableStatefulSessionResumption: true
        disableStatelessSessionResumption: true
  listenerFilters:
  - name: envoy.filters.listener.tls_inspector
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
  maxConnectionsToAcceptPerSocketEvent: 1
  name: tls-listener-terminate
  perConnectionBufferLimitBytes: 32768
