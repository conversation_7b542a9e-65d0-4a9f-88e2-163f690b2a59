- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: httproute/envoy-gateway/httproute-btls/rule/0
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: httproute/envoy-gateway/httproute-btls/rule/0
  perConnectionBufferLimitBytes: 32768
  transportSocketMatches:
  - match:
      name: httproute/envoy-gateway/httproute-btls/rule/0/tls/0
    name: httproute/envoy-gateway/httproute-btls/rule/0/tls/0
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        commonTlsContext:
          combinedValidationContext:
            defaultValidationContext:
              matchTypedSubjectAltNames:
              - matcher:
                  exact: example.com
                sanType: DNS
              - matcher:
                  exact: spiffe://cluster.local/ns/istio-demo/sa/echo-v1
                sanType: URI
              - matcher:
                  exact: subdomain.secondexample.com
                sanType: DNS
            validationContextSdsSecretConfig:
              name: policy-btls/policies-ca
              sdsConfig:
                ads: {}
                resourceApiVersion: V3
        sni: example.com
  type: EDS
