- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: direct-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: direct-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxConnections: 2048
      maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 15s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: tracing-0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: otel-collector.default.svc.cluster.local
              portValue: 4317
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: tracing-0/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: tracing-0
  outlierDetection:
    baseEjectionTime: 30s
    consecutive5xx: 5
    consecutiveGatewayFailure: 4
    consecutiveLocalOriginFailure: 5
    interval: 5s
    maxEjectionPercent: 10
  perConnectionBufferLimitBytes: 20971520
  respectDnsTtl: true
  transportSocket:
    name: envoy.transport_sockets.upstream_proxy_protocol
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.transport_sockets.proxy_protocol.v3.ProxyProtocolUpstreamTransport
      config:
        version: V2
      transportSocket:
        name: envoy.transport_sockets.raw_buffer
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.transport_sockets.raw_buffer.v3.RawBuffer
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions:
          initialConnectionWindowSize: 1048576
          initialStreamWindowSize: 65536
  upstreamConnectionOptions:
    tcpKeepalive:
      keepaliveProbes: 7
