- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: third-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: third-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  dnsRefreshRate: 30s
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadAssignment:
    clusterName: securitypolicy/envoy-gateway/policy-for-gateway/0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: oauth.foo.com
              portValue: 443
        loadBalancingWeight: 1
        metadata:
          filterMetadata:
            envoy.transport_socket_match:
              name: securitypolicy/envoy-gateway/policy-for-gateway/0/tls/0
      loadBalancingWeight: 1
      locality:
        region: securitypolicy/envoy-gateway/policy-for-gateway/0/backend/0
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: securitypolicy/envoy-gateway/policy-for-gateway/0
  perConnectionBufferLimitBytes: 32768
  respectDnsTtl: true
  transportSocketMatches:
  - match:
      name: securitypolicy/envoy-gateway/policy-for-gateway/0/tls/0
    name: securitypolicy/envoy-gateway/policy-for-gateway/0/tls/0
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        commonTlsContext:
          combinedValidationContext:
            defaultValidationContext:
              matchTypedSubjectAltNames:
              - matcher:
                  exact: oauth.foo.com
                sanType: DNS
            validationContextSdsSecretConfig:
              name: policy-btls-backend-fqdn/envoy-gateway-ca
              sdsConfig:
                ads: {}
                resourceApiVersion: V3
        sni: oauth.foo.com
  type: STRICT_DNS
