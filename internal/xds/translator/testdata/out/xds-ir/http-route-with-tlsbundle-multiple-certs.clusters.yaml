- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: httproute/envoy-gateway/httproute-btls/rule/0
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: httproute/envoy-gateway/httproute-btls/rule/0
  perConnectionBufferLimitBytes: 32768
  transportSocketMatches:
  - match:
      name: httproute/envoy-gateway/httproute-btls/rule/0/tls/0
    name: httproute/envoy-gateway/httproute-btls/rule/0/tls/0
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        commonTlsContext:
          combinedValidationContext:
            defaultValidationContext:
              matchTypedSubjectAltNames:
              - matcher:
                  exact: bar.example.com
                sanType: DNS
            validationContextSdsSecretConfig:
              name: policy-btls/policies-ca2
              sdsConfig:
                ads: {}
                resourceApiVersion: V3
        sni: bar.example.com
  - match:
      name: httproute/envoy-gateway/httproute-btls/rule/0/tls/1
    name: httproute/envoy-gateway/httproute-btls/rule/0/tls/1
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        commonTlsContext:
          combinedValidationContext:
            defaultValidationContext:
              matchTypedSubjectAltNames:
              - matcher:
                  exact: example.com
                sanType: DNS
            validationContextSdsSecretConfig:
              name: policy-btls/policies-ca
              sdsConfig:
                ads: {}
                resourceApiVersion: V3
        sni: example.com
  type: EDS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: httproute/envoy-gateway/httproute-btls-2/rule/0
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.least_request
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.least_request.v3.LeastRequest
          localityLbConfig:
            localityWeightedLbConfig: {}
  name: httproute/envoy-gateway/httproute-btls-2/rule/0
  perConnectionBufferLimitBytes: 32768
  transportSocket:
    name: envoy.transport_sockets.upstream_proxy_protocol
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.transport_sockets.proxy_protocol.v3.ProxyProtocolUpstreamTransport
      config:
        version: V2
      transportSocket:
        name: envoy.transport_sockets.raw_buffer
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.transport_sockets.raw_buffer.v3.RawBuffer
  transportSocketMatches:
  - match:
      name: httproute/envoy-gateway/httproute-btls-2/rule/0/tls/1
    name: httproute/envoy-gateway/httproute-btls-2/rule/0/tls/1
    transportSocket:
      name: envoy.transport_sockets.upstream_proxy_protocol
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.proxy_protocol.v3.ProxyProtocolUpstreamTransport
        config:
          version: V2
        transportSocket:
          name: envoy.transport_sockets.tls
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
            commonTlsContext:
              combinedValidationContext:
                defaultValidationContext:
                  matchTypedSubjectAltNames:
                  - matcher:
                      exact: example.com
                    sanType: DNS
                validationContextSdsSecretConfig:
                  name: policy-btls-2/policies-ca
                  sdsConfig:
                    ads: {}
                    resourceApiVersion: V3
            sni: example.com
  type: EDS
