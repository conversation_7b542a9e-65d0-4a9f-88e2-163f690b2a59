- ignorePortInHostMatching: true
  name: test-listener-modify
  virtualHosts:
  - domains:
    - '*'
    name: test-listener-modify/*
    routes:
    - match:
        prefix: /
      name: test-route-modify
      route:
        cluster: test-route-dest
        upgradeConfigs:
        - upgradeType: websocket
- ignorePortInHostMatching: true
  name: second-listener
  virtualHosts:
  - domains:
    - example.com
    name: second-listener/example_com
    routes:
    - match:
        pathSeparatedPrefix: /api
      name: second-route
      route:
        cluster: second-route-dest
        upgradeConfigs:
        - upgradeType: websocket
- name: extension-injected-route
  responseHeadersToAdd:
  - header:
      key: x-extension-injected
      value: route
