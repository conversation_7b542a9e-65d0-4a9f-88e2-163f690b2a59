- clusterName: test-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: test-route-dest/backend/0
- clusterName: second-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 8080
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: second-route-dest/backend/0
